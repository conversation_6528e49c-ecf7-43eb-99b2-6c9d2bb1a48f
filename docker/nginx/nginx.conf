server {
    listen 80;
    server_name localhost;
    root /var/www/html/public;
    server_tokens off;

    autoindex off;
    etag off;

    if ($server_protocol ~* "HTTP/1.0") {
        return 444;
    }
    ssi off;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Frame-Options "SAMEORIGIN" always;

    proxy_cookie_path / "/; HttpOnly; Secure";
    add_header Set-Cookie "Path=/; HttpOnly; Secure";

    if ($request_method = TRACE) {
        return 405;
    }

    if ($request_method !~ ^(GET|HEAD|POST|PUT|DELETE)$) {
        return 405;
    }

    index index.php;
    charset utf-8;

    client_max_body_size 500M;
    client_body_timeout 300s;
    client_header_timeout 60s;
    send_timeout 300s;
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    client_body_temp_path /tmp/nginx_uploads;
    client_body_in_file_only clean;

    gzip on;
    gzip_vary on;
    gzip_min_length 1000;
    gzip_comp_level 6;
    gzip_proxied any;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        application/x-font-ttf
        application/vnd.ms-fontobject
        font/opentype;

    location /qr/ {
        try_files $uri $uri/ /index.php?$query_string;
        client_max_body_size 100M;
        client_body_timeout 120s;
        gzip on;
        gzip_types text/plain application/json;
    }

    location /upload/ {
        try_files $uri $uri/ /index.php?$query_string;
        client_max_body_size 500M;
        client_body_timeout 600s;
        send_timeout 600s;
    }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
        autoindex off;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|zip|rar|doc|docx)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        etag off;
        if_modified_since off;
        add_header Last-Modified "";
        client_max_body_size 500M;
    }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass app:9000;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;

        fastcgi_read_timeout 900;
        fastcgi_send_timeout 900;
        fastcgi_connect_timeout 60;

        fastcgi_buffers 32 32k;
        fastcgi_buffer_size 64k;
        fastcgi_busy_buffers_size 128k;
        fastcgi_temp_file_write_size 128k;

        fastcgi_hide_header X-Powered-By;
        fastcgi_param PHP_VALUE "
            upload_max_filesize=500M
            post_max_size=500M
            memory_limit=512M
            max_execution_time=900
            max_input_time=900
            expose_php=Off
            file_uploads=On
            upload_tmp_dir=/tmp";
    }

    location ~ /\.(?!well-known).* {
        limit_except GET POST { deny all; }
        deny all;
    }

    location ~ \.(htaccess|htpasswd|conf|ini|txt|log)$ {
        deny all;
    }

    location ~ ^/tmp/ {
        deny all;
    }
}
